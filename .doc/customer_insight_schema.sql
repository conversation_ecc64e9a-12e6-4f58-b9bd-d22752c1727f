-- =====================================================
-- 客户洞察助手系统数据库 Schema（正式版）
-- PostgreSQL
-- 说明：结构优化，约束合理化，注释精简，增加常用视图
-- =====================================================

-- 1. 维度配置表
CREATE TABLE qc_ai_dimension_config (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    agent_id BIGINT NOT NULL,
    dimension_code VARCHAR(20) NOT NULL,
    dimension_name VARCHAR(100),
    interpretation_prompt TEXT,
    sort_order INTEGER DEFAULT 0
);
-- 表注释
COMMENT ON TABLE qc_ai_dimension_config IS '维度配置表，定义智能体的各种分析维度';
COMMENT ON COLUMN qc_ai_dimension_config.id IS '维度配置ID，主键';
COMMENT ON COLUMN qc_ai_dimension_config.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_dimension_config.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_dimension_config.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_dimension_config.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_dimension_config.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_dimension_config.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_dimension_config.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_dimension_config.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN qc_ai_dimension_config.dimension_code IS '维度编码：ORDER-订单, DISPLAY-铺货, VIVID-生动化, PAID_DISPLAY-付费陈列, COUPON-兑换券, ASSET-资产, AUTHENTICITY-真实性';
COMMENT ON COLUMN qc_ai_dimension_config.dimension_name IS '维度展示名称';
COMMENT ON COLUMN qc_ai_dimension_config.interpretation_prompt IS '大模型解读提示词，指导AI如何分析该维度';
COMMENT ON COLUMN qc_ai_dimension_config.sort_order IS '排序顺序，控制前端展示顺序';


-- 2. 数据源表
CREATE TABLE IF NOT EXISTS qc_ai_data_source (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1),
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    source_name VARCHAR(100),
    source_code VARCHAR(50),
    api_url VARCHAR(255),
    http_method VARCHAR(10),
    description TEXT,
    belong_dimension_code VARCHAR(50),
    belong_dimension_name VARCHAR(100),
    query_time_start_field VARCHAR(100),
    query_time_end_field VARCHAR(100),
    query_field_name VARCHAR(100)
);

COMMENT ON TABLE qc_ai_data_source IS '数据源表，定义可用的接口和数据来源';
COMMENT ON COLUMN qc_ai_data_source.id IS '数据源ID，主键';
COMMENT ON COLUMN qc_ai_data_source.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_data_source.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_data_source.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_data_source.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_data_source.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_data_source.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_data_source.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_data_source.source_name IS '数据源名称';
COMMENT ON COLUMN qc_ai_data_source.source_code IS '数据源编码，全局唯一标识';
COMMENT ON COLUMN qc_ai_data_source.api_url IS '接口url';
COMMENT ON COLUMN qc_ai_data_source.http_method IS '请求方式：POST, GET';
COMMENT ON COLUMN qc_ai_data_source.description IS '数据源描述说明';
COMMENT ON COLUMN qc_ai_data_source.belong_dimension_code IS '数据源从属维度编码，关联qc_ai_dimension_config.dimension_code';
COMMENT ON COLUMN qc_ai_data_source.belong_dimension_name IS '数据源从属维度名称';
COMMENT ON COLUMN qc_ai_data_source.query_time_start_field IS '查询范围开始字段';
COMMENT ON COLUMN qc_ai_data_source.query_time_end_field IS '查询范围结束字段';
COMMENT ON COLUMN qc_ai_data_source.query_field_name IS '查询范围名称';

-- 3. 数据源引用数据项定义表
CREATE TABLE IF NOT EXISTS qc_ai_data_source_ref_data_item (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    item_code VARCHAR(50) NOT NULL UNIQUE,
    item_name VARCHAR(100) NOT NULL,
    query_business_code VARCHAR(50),
    data_type_code VARCHAR(10),
    placeholder_name VARCHAR(50),
    description TEXT,
    sort_order INTEGER
);
COMMENT ON TABLE qc_ai_data_source_ref_data_item IS '数据源引用数据项定义表，定义数据源引用的数据项';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.id IS '数据项ID，主键';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.item_code IS '数据项编码，全局唯一标识';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.item_name IS '数据项显示名称';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.query_business_code IS '关联的查询业务--对应qc_ai_data_source.source_code';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.data_type_code IS '数据类型编码：DETAIL-明细, METRIC-指标';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.placeholder_name IS '在提示词中的占位符，如 ${orderRate}';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.description IS '数据项描述说明';
COMMENT ON COLUMN qc_ai_data_source_ref_data_item.sort_order IS '排序顺序，控制前端展示顺序';

-- 5. 维度数据项关系表
CREATE TABLE IF NOT EXISTS qc_ai_dimension_ref_data_item_rel (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1),
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    dimension_config_id BIGINT,
    ref_data_item_id BIGINT,
    sort_order INTEGER,
    query_value VARCHAR(255)
);
COMMENT ON TABLE qc_ai_dimension_ref_data_item_rel IS '维度与数据项关系';

-- 表注释
COMMENT ON TABLE qc_ai_dimension_ref_data_item_rel IS '维度数据项关系表，定义维度使用哪些引用数据源及查询参数';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.id IS '关系ID，主键';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.dimension_config_id IS '维度配置ID，关联qc_ai_dimension_config';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.ref_data_item_id IS '引用数据项ID，关联qc_ai_data_source_ref_data_item';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.sort_order IS '数据源在维度中的排序';
COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.query_value IS '查询参数值 1-最近1个月 2-最近2个月 3-最近3个月';

-- 确保 query_value 字段存在（如果不存在则添加）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'qc_ai_dimension_ref_data_item_rel' 
        AND column_name = 'query_value'
    ) THEN
        ALTER TABLE qc_ai_dimension_ref_data_item_rel ADD COLUMN query_value VARCHAR(255);
        COMMENT ON COLUMN qc_ai_dimension_ref_data_item_rel.query_value IS '查询参数值 1-最近1个月 2-最近2个月 3-最近3个月';
    END IF;
END $$;
-- =====================================================
-- 6. 衡量标准表
-- =====================================================
CREATE TABLE qc_ai_measurement_standard (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    standard_name VARCHAR(100) NOT NULL,
    standard_definition TEXT,
    standard_type_code VARCHAR(10),
    standard_type VARCHAR(20),
    sort_order INTEGER DEFAULT 0
);
COMMENT ON TABLE qc_ai_measurement_standard IS '衡量标准表';

-- 表注释
COMMENT ON TABLE qc_ai_measurement_standard IS '衡量标准表，定义评价标准';
COMMENT ON COLUMN qc_ai_measurement_standard.id IS '标准ID，主键';
COMMENT ON COLUMN qc_ai_measurement_standard.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_measurement_standard.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_measurement_standard.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_measurement_standard.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_measurement_standard.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_measurement_standard.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_measurement_standard.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_name IS '标准名称，如"健康"、"良好"、"达标"等';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_definition IS '标准定义，如"总分≥6分判定为健康"';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_type_code IS '标准类型编码：DIMENSION-维度标准，SUMMARY-总结标准';
COMMENT ON COLUMN qc_ai_measurement_standard.standard_type IS '标准类型中文名称：维度标准、总结标准';
COMMENT ON COLUMN qc_ai_measurement_standard.sort_order IS '排序顺序，控制前端展示顺序';

-- =====================================================
-- 7. 总结配置表
-- =====================================================
CREATE TABLE qc_ai_summary_config (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    agent_id BIGINT NOT NULL,
    comprehensive_prompt TEXT,
    summary_advice_prompt TEXT
);
COMMENT ON TABLE qc_ai_summary_config IS '总结配置表';

-- 表注释
COMMENT ON TABLE qc_ai_summary_config IS '总结配置表，定义综合分析和建议生成规则';
COMMENT ON COLUMN qc_ai_summary_config.id IS '总结配置ID，主键';
COMMENT ON COLUMN qc_ai_summary_config.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_summary_config.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_summary_config.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_summary_config.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_summary_config.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_summary_config.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_summary_config.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_summary_config.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN qc_ai_summary_config.comprehensive_prompt IS '综合衡量标准提示词，引导模型分析维度表现';
COMMENT ON COLUMN qc_ai_summary_config.summary_advice_prompt IS '总结及建议提示词，引导模型生成最终建议';

-- =====================================================
-- 8. 配置标准关联表
-- =====================================================
CREATE TABLE qc_ai_config_standard_rel (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    config_id BIGINT NOT NULL,
    config_type VARCHAR(10),
    standard_id BIGINT NOT NULL
);
COMMENT ON TABLE qc_ai_config_standard_rel IS '配置与标准关联';

-- 表注释
COMMENT ON TABLE qc_ai_config_standard_rel IS '配置与衡量标准关联表，维度/总结多对多统一';
COMMENT ON COLUMN qc_ai_config_standard_rel.id IS '关系ID，主键';
COMMENT ON COLUMN qc_ai_config_standard_rel.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_config_standard_rel.creator_id IS '创建人ID';
COMMENT ON COLUMN qc_ai_config_standard_rel.creator_name IS '创建人姓名';
COMMENT ON COLUMN qc_ai_config_standard_rel.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_config_standard_rel.modifyier_id IS '修改人ID';
COMMENT ON COLUMN qc_ai_config_standard_rel.modifyier_name IS '修改人姓名';
COMMENT ON COLUMN qc_ai_config_standard_rel.modify_time IS '修改时间';
COMMENT ON COLUMN qc_ai_config_standard_rel.config_id IS 'qc_ai_summary_config或者dimension_config_id';
COMMENT ON COLUMN qc_ai_config_standard_rel.config_type IS '标准所属分配编码：DIMENSION-维度标准，SUMMARY-总结标准';
COMMENT ON COLUMN qc_ai_config_standard_rel.standard_id IS '标准ID，关联qc_ai_measurement_standard';

-- =====================================================
-- 9. 对话记录扩展表
-- =====================================================
CREATE TABLE qc_ai_conversation_ext (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    conversation_id BIGINT NOT NULL,
    insight_object_id BIGINT,
    insight_object_name VARCHAR(200),
    diagnosis_conclusion TEXT,
    order_insight_evaluation TEXT,
    order_insight_result TEXT,
    display_evaluation TEXT,
    display_insight_result TEXT,
    summary_advice TEXT
);
COMMENT ON TABLE qc_ai_conversation_ext IS '对话扩展表';

-- 表注释
COMMENT ON TABLE qc_ai_conversation_ext IS '对话记录扩展表，存储洞察分析的详细结果和日志';
COMMENT ON COLUMN qc_ai_conversation_ext.id IS '扩展记录ID，主键';
COMMENT ON COLUMN qc_ai_conversation_ext.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_conversation_ext.conversation_id IS '对话ID，关联qc_ai_agent_conversation表的id';
COMMENT ON COLUMN qc_ai_conversation_ext.insight_object_id IS '洞察对象ID，客户或经销商的ID';
COMMENT ON COLUMN qc_ai_conversation_ext.insight_object_name IS '洞察对象名称，客户或经销商的名字';
COMMENT ON COLUMN qc_ai_conversation_ext.diagnosis_conclusion IS '诊断结论，模型生成的总体结论';
COMMENT ON COLUMN qc_ai_conversation_ext.order_insight_evaluation IS '订单洞察评价，模型生成的订单洞察评价';
COMMENT ON COLUMN qc_ai_conversation_ext.order_insight_result IS '订单洞察结果，模型生成的订单洞察结果';
COMMENT ON COLUMN qc_ai_conversation_ext.display_evaluation IS '铺货评价，模型生成的铺货评价';
COMMENT ON COLUMN qc_ai_conversation_ext.display_insight_result IS '铺货洞察结果，模型生成的铺货洞察结果';
COMMENT ON COLUMN qc_ai_conversation_ext.summary_advice IS '总结及建议，模型生成的总结及建议';

-- 10.洞察数据源记录表
CREATE TABLE qc_ai_insight_data_source_record (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    customer_id BIGINT,
    customer_name VARCHAR(100),
    dimension_ref_data_item_rel_id BIGINT,
    value TEXT
);
COMMENT ON TABLE qc_ai_insight_data_source_record IS '洞察数据源结果记录';
COMMENT ON COLUMN qc_ai_insight_data_source_record.id IS '记录ID，主键';
COMMENT ON COLUMN qc_ai_insight_data_source_record.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_insight_data_source_record.create_time IS '创建时间';
COMMENT ON COLUMN qc_ai_insight_data_source_record.customer_id IS '客户ID';
COMMENT ON COLUMN qc_ai_insight_data_source_record.customer_name IS '客户名称';
COMMENT ON COLUMN qc_ai_insight_data_source_record.dimension_ref_data_item_rel_id IS '维度数据项关系表ID';
COMMENT ON COLUMN qc_ai_insight_data_source_record.value IS '引用数据源结果';

CREATE TABLE qc_ai_main_prompt (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    -- 提示词基本信息
    prompt_name VARCHAR(100) NOT NULL,           -- 提示词名称
    prompt_type VARCHAR(20) NOT NULL,           -- 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
    
    -- 关联信息
    config_id BIGINT -- 关联qc_ai_summary_config或者qc_ai_dimension_config的id
);
COMMENT ON TABLE qc_ai_main_prompt IS '主提示词表';
COMMENT ON COLUMN qc_ai_main_prompt.id IS '主提示词ID，主键';
COMMENT ON COLUMN qc_ai_main_prompt.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_name IS '提示词名称';
COMMENT ON COLUMN qc_ai_main_prompt.prompt_type IS '提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE';
COMMENT ON COLUMN qc_ai_main_prompt.config_id IS '关联qc_ai_summary_config或者qc_ai_dimension_config的id';

CREATE TABLE qc_ai_prompt_fragment (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    -- 关联信息
    main_prompt_id BIGINT NOT NULL,             -- 关联主提示词ID
    
    -- 片段信息
    fragment_key VARCHAR(100) NOT NULL,         -- 片段键名
    fragment_value TEXT NOT NULL,               -- 片段值
    sort_order INTEGER DEFAULT 0               -- 排序号
);
COMMENT ON TABLE qc_ai_prompt_fragment IS '提示词片段表';
COMMENT ON COLUMN qc_ai_prompt_fragment.id IS '提示词片段ID，主键';
COMMENT ON COLUMN qc_ai_prompt_fragment.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_prompt_fragment.main_prompt_id IS '关联主提示词ID';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_key IS '片段键名';
COMMENT ON COLUMN qc_ai_prompt_fragment.fragment_value IS '片段值';
COMMENT ON COLUMN qc_ai_prompt_fragment.sort_order IS '排序号';

create table qc_ai_dimension_prompt_value
(
    id                  bigserial
        primary key,
    status              char default '1'::bpchar,
    dimension_config_id bigint not null,
    prompt_fragment_id  bigint not null,
    fragment_value      text   not null
);

comment on table qc_ai_dimension_prompt_value is '维度提示词片段值表';
comment on column qc_ai_dimension_prompt_value.id is '维度提示词片段值ID，主键';
comment on column qc_ai_dimension_prompt_value.status is '逻辑删除状态：1-有效，0-无效';
comment on column qc_ai_dimension_prompt_value.dimension_config_id is '维度配置ID，关联qc_ai_dimension_config表';
comment on column qc_ai_dimension_prompt_value.prompt_fragment_id is '提示词片段ID，关联qc_ai_prompt_fragment表';
comment on column qc_ai_dimension_prompt_value.fragment_value is '片段值';
-- =========================
-- 常用业务视图
-- =========================
-- 维度与数据项明细视图
CREATE OR REPLACE VIEW v_dimension_data_items AS
SELECT d.id AS dimension_id, d.dimension_name, r.id AS rel_id, i.item_code, i.item_name, i.data_type_code
FROM qc_ai_dimension_config d
JOIN qc_ai_dimension_ref_data_item_rel r ON d.id = r.dimension_config_id
JOIN qc_ai_data_source_ref_data_item i ON r.ref_data_item_id = i.id;

-- 数据源与数据项明细视图
CREATE OR REPLACE VIEW v_data_source_items AS
SELECT s.id AS data_source_id, s.source_name, r.ref_data_item_id, i.item_code, i.item_name
FROM qc_ai_data_source s
JOIN qc_ai_data_source_item_rel r ON s.id = r.data_source_id
JOIN qc_ai_data_source_ref_data_item i ON r.ref_data_item_id = i.id;

-- 洞察结果聚合视图
CREATE OR REPLACE VIEW v_insight_result_summary AS
SELECT r.id, r.create_time, r.customer_id, r.customer_name, d.dimension_name, i.item_name, r.value
FROM qc_ai_insight_data_source_record r
LEFT JOIN qc_ai_dimension_ref_data_item_rel rel ON r.dimension_ref_data_item_rel_id = rel.id
LEFT JOIN qc_ai_dimension_config d ON rel.dimension_config_id = d.id
LEFT JOIN qc_ai_data_source_ref_data_item i ON rel.ref_data_item_id = i.id;


-- 插入客户洞察助手Agent数据
INSERT INTO qc_ai_agent (
    id, status, creator_id, creator_name, create_time, modifyier_id, modifyier_name, modify_time,
    model_id, sequ, name, logo, description, prompt, leading_question, category_id,
    context_search_amount, introduction, internet_search, null_result_answer, error_result_answer,
    data_fetch_url, internal_flag, publish_flag, publish_time, model_temperature, model_top_p,
    model_max_tokens, enable, publish_user_id, publish_user_name, max_recall_count,
    min_match_threshold, search_scope, qa_min_match_threshold, split_sql_prompt, data_range,
    biz_prompt, show_chat_log_content_type, intent_is_enabled, h5_url
) VALUES (
    7, '1', -999, '系统管理员', CURRENT_TIMESTAMP, -999, '系统管理员', CURRENT_TIMESTAMP,
    82, 7.00, '客户洞察助手', 'https://res.waiqin365.com/d/static/agent/insightIcon.png',
    '深度分析客户业务表现，提供专业洞察报告和建议',
    '# 角色定位
你是一位专业的【勤策客户洞察助手】。

# 人设与沟通风格
- **专家形象**: 你是经验丰富的业务分析专家，具备深厚的数据洞察能力。
- **专业严谨**: 你的语言准确、客观、具有分析性，提供基于数据的专业见解。
- **洞察深入**: 你能从多维度数据中发现问题本质，提供具有指导意义的建议。

# 核心能力
1. **业务数据分析**: 深度分析客户在各业务维度的表现数据。
2. **问题诊断识别**: 识别业务异常和潜在风险点。
3. **策略建议输出**: 基于分析结果提供针对性的改进建议。

# 行为准则
- **身份识别**: 当用户询问"你是谁"或类似问题时，你必须严格按照以下格式回答："我是勤策客户洞察助手，我可以为您提供客户业务表现分析、多维度数据洞察、问题诊断和改进建议等服务。"
- **坚守角色**: 在任何对话中，都不要脱离你作为客户洞察助手的角色。',
    '["分析张三客户的整体业务表现","李四经销商的铺货情况如何？","王五门店的订单满足率怎么样？"]',
    NULL, 5, '您好！欢迎使用客户洞察助手。我可以为您深度分析客户业务表现，提供专业的洞察报告和改进建议。',
    '0', '抱歉，我无法获取到相关的客户洞察信息。如果您有其他需要分析的客户，请告诉我。',
    '系统繁忙中，正在为您生成专业的洞察分析报告，请稍候...', NULL, '1', NULL, NULL,
    0.7, 0.8, 2048, '1', NULL, NULL, 3, 0.6, '1', 0.85, NULL, NULL, '',
    '1', 0, '/sysapp/react/h5/agent.html#/suspensionDialogue/dialogue?agentId=7&cmId={cmId}'
);

-- 插入 qc_ai_data_source 数据源表数据
INSERT INTO qc_ai_data_source (id, status, source_name, source_code, api_url, http_method, description)
VALUES
(1, '1', '分销订单', 'distributionOrder', '', '', '分销订单相关数据源'),
(2, '1', '铺货上报', 'stockReport', '', '', '铺货上报相关数据源'),
(3, '1', '铺货上报（old）', 'stockOld', '', '', '铺货上报（old）相关数据源'),
(4, '1', '拜访', 'visit', '', '', '拜访相关数据源'),
(5, '1', '付费列', 'paidColumn', '', '', '付费列相关数据源'),
(6, '1', '兑换券', 'coupon', '', '', '兑换券相关数据源'),
(7, '1', '资产投放', 'assetPlacement', '', '', '资产投放相关数据源'),
(8, '1', '翻拍', 'rephotograph', '', '', '翻拍相关数据源'),
(9, '1', '窜拍', 'crossPhotograph', '', '', '窜拍相关数据源'),
(10, '1', '客户主数据', 'customerMaster', '', '', '客户主数据相关数据源'),
(11, '1', '客户联系人', 'customerContact', '', '', '客户联系人相关数据源');

-- 插入 qc_ai_data_source_ref_data_item 数据项定义表数据
INSERT INTO qc_ai_data_source_ref_data_item
(id, status, item_code, item_name, query_business_code, data_type_code, placeholder_name, description, sort_order)
VALUES
(1, '1', 'distributionOrderSatisfactionRate', '订单满足率', 'distributionOrder', 'METRIC', '${distributionOrderSatisfactionRate}', '订单状态（已批通过）的发货SKU数/订单中SKU数,转成基本单位', 1),
(2, '1', 'distributionOrderAllocationRate', '订单配货率', 'distributionOrder', 'METRIC', '${distributionOrderAllocationRate}', '已发货SKU数/订单中SKU数,转成基本单位', 2),
(3, '1', 'distributionOrderDetailFields', '分销订单明细', 'distributionOrder', 'DETAIL', '${distributionOrderDetailFields}', '下单时间2025-06-18，最后发货时间2025-06-20', 3),
(4, '1', 'distributionOrderDetailFields.field1', '分销订单明细-选择字段1', 'distributionOrder', 'DETAIL', '${distributionOrderDetailFields.field1}', '下单时间2025-06-18，最后发货时间2025-06-20', 4),
(5, '1', 'distributionOrderDetailFields.field2', '分销订单明细-选择字段2', 'distributionOrder', 'DETAIL', '${distributionOrderDetailFields.field2}', '下单时间2025-06-18，最后发货时间2025-06-20', 5),
(6, '1', 'stockReportQualifiedStatus', '铺货记录合格状态', 'stockReport', 'DETAIL', '${stockReportQualifiedStatus}', 'BY铺货记录看是否合格', 6),
(7, '1', 'stockCustom', '铺货率（定制）', 'stockOld', 'METRIC', '${stockCustom}', '铺货SKU/铺货标准SKU', 7),
(8, '1', 'visitSubtaskFieldValue', '拜访子任务字段值', 'visit', 'DETAIL', '${visitSubtaskFieldValue}', '拜访表单子任务字段、表单中的字段表单单', 8),
(9, '1', 'paidColumnEffectivenessRatio', '费效比', 'paidColumn', 'METRIC', '${paidColumnEffectivenessRatio}', 'BY客户资效比数据，按投放批次、当前客户画像维度统计', 9),
(10, '1', 'couponDetail', '兑换券明细', 'coupon', 'DETAIL', '${couponDetail}', 'BY门店在途或已兑换的兑换券的发放明细、名称、有效期', 10),
(11, '1', 'assetPlacementDetail', '资产投放明细', 'assetPlacement', 'DETAIL', '${assetPlacementDetail}', '当前在投', 11),
(12, '1', 'rephotographDetail', '翻拍明细', 'rephotograph', 'DETAIL', '${rephotographDetail}', 'BY客户翻拍照片数量及明细', 12),
(13, '1', 'crossPhotographDetail', '窜拍明细', 'crossPhotograph', 'DETAIL', '${crossPhotographDetail}', '基于客户的窜拍数量及明细', 13),
(14, '1', 'customerMasterData', '客户主数据', 'customerMaster', 'DETAIL', '${customerMasterData}', '客户主数据表，地址是否标准、地理是否标准', 14),
(15, '1', 'customerContact', '客户联系人', 'customerContact', 'DETAIL', '${customerContact}', '联系人表，联系人数量、联系人信息补全、联系人手机号是否标准', 15),
(16, '1', 'SimilarPhotos', '相似照片', 'customerContact', 'DETAIL', '${SimilarPhotos}', '疑似虚假照片：当一个活动周期内门店比如拍货架or冰箱的照片相似度为某个较低区间，即完全不一样', 16);
