# 客户洞察助手数据库表结构更新指南

## 新增表：qc_ai_dimension_prompt_value

### 表结构说明
该表用于维护维度/总结配置与提示词段落ID及值的关系，支持灵活的提示词配置管理。

### 表定义
```sql
CREATE TABLE IF NOT EXISTS qc_ai_dimension_prompt_value (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    creator_id BIGINT,
    creator_name VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifyier_id BIGINT,
    modifyier_name VARCHAR(100),
    modify_time TIMESTAMP,
    
    -- 关联配置信息
    config_id BIGINT NOT NULL,                    -- 关联的配置ID
    config_type VARCHAR(20) NOT NULL,             -- 配置类型：DIMENSION/SUMMARY
    
    -- 提示词段落信息
    prompt_fragment_id BIGINT,                   -- 关联的提示词片段ID
    fragment_key VARCHAR(100),                    -- 提示词片段键名
    fragment_value TEXT,                         -- 提示词片段值
    
    -- 业务标识
    dimension_code VARCHAR(20),                   -- 维度编码
    prompt_type VARCHAR(20),                    -- 提示词类型
    
    -- 排序
    sort_order INTEGER DEFAULT 0
);
```

### 字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| config_id | BIGINT | 关联的配置ID（维度配置或总结配置） |
| config_type | VARCHAR(20) | 配置类型：DIMENSION-维度配置，SUMMARY-总结配置 |
| prompt_fragment_id | BIGINT | 关联的提示词片段ID |
| fragment_key | VARCHAR(100) | 提示词片段键名 |
| fragment_value | TEXT | 提示词片段值 |
| dimension_code | VARCHAR(20) | 维度编码（当config_type为DIMENSION时使用） |
| prompt_type | VARCHAR(20) | 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE |

### 索引创建
```sql
CREATE INDEX IF NOT EXISTS idx_dimension_prompt_config_id ON qc_ai_dimension_prompt_value(config_id);
CREATE INDEX IF NOT EXISTS idx_dimension_prompt_config_type ON qc_ai_dimension_prompt_value(config_type);
CREATE INDEX IF NOT EXISTS idx_dimension_prompt_dimension_code ON qc_ai_dimension_prompt_value(dimension_code);
CREATE INDEX IF NOT EXISTS idx_dimension_prompt_prompt_type ON qc_ai_dimension_prompt_value(prompt_type);
```

### 视图创建
```sql
CREATE OR REPLACE VIEW v_dimension_prompt_config AS
SELECT 
    dpv.id,
    dpv.config_id,
    dpv.config_type,
    dpv.dimension_code,
    dpv.prompt_type,
    dpv.fragment_key,
    dpv.fragment_value,
    dpv.sort_order,
    dc.dimension_name,
    dc.interpretation_prompt,
    sc.comprehensive_prompt,
    sc.summary_advice_prompt
FROM qc_ai_dimension_prompt_value dpv
LEFT JOIN qc_ai_dimension_config dc ON dpv.config_id = dc.id AND dpv.config_type = 'DIMENSION'
LEFT JOIN qc_ai_summary_config sc ON dpv.config_id = sc.id AND dpv.config_type = 'SUMMARY'
WHERE dpv.status = '1';
```

### 示例数据
```sql
INSERT INTO qc_ai_dimension_prompt_value (config_id, config_type, dimension_code, prompt_type, fragment_key, fragment_value, sort_order) VALUES
(1, 'DIMENSION', 'ORDER', 'DIMENSION', 'order_analysis_intro', '基于以下订单数据进行分析：', 1),
(1, 'DIMENSION', 'ORDER', 'DIMENSION', 'order_analysis_focus', '重点关注订单满足率、配送时效和客户满意度', 2),
(1, 'SUMMARY', NULL, 'SUMMARY_COMPREHENSIVE', 'summary_intro', '基于各维度分析结果，综合评估客户整体表现：', 1),
(1, 'SUMMARY', NULL, 'SUMMARY_ADVICE', 'advice_intro', '根据综合分析结果，为客户提供具体改进建议：', 1);
```

## 使用场景
1. **维度配置管理**：为每个维度配置自定义的提示词段落
2. **总结配置管理**：为总结配置配置综合分析和建议提示词
3. **动态提示词**：支持根据不同业务场景动态调整提示词内容
4. **版本管理**：通过status字段实现提示词配置的版本控制