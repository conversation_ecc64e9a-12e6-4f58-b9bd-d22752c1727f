<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.DimensionPromptValueMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="dimension_config_id" property="dimensionConfigId"/>
        <result column="prompt_fragment_id" property="promptFragmentId"/>
        <result column="fragment_value" property="fragmentValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, status, dimension_config_id, prompt_fragment_id, fragment_value
    </sql>

    <!-- 根据维度配置ID查询提示词片段值列表 -->
    <select id="selectByDimensionConfigId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_prompt_value
        WHERE dimension_config_id = #{dimensionConfigId}
