package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维度提示词片段值Mapper接口
 */
public interface DimensionPromptValueMapper {
    
    /**
     * 根据维度配置ID查询提示词片段值列表
     *
     * @param dimensionConfigId 维度配置ID
     * @return 提示词片段值列表
     */
    List<DimensionPromptValue> selectByDimensionConfigId(@Param("dimensionConfigId") Long dimensionConfigId);
    
    /**
     * 根据维度配置ID和提示词片段ID查询提示词片段值
     *
     * @param dimensionConfigId 维度配置ID
     * @param promptFragmentId 提示词片段ID
     * @return 提示词片段值
     */
    DimensionPromptValue selectByDimensionConfigIdAndPromptFragmentId(@Param("dimensionConfigId") Long dimensionConfigId, 
                                                                       @Param("promptFragmentId") Long promptFragmentId);
    
    /**
     * 插入提示词片段值
     *
     * @param dimensionPromptValue 提示词片段值
     * @return 影响行数
     */
    int insert(DimensionPromptValue dimensionPromptValue);
    
    /**
     * 批量插入提示词片段值
     *
     * @param dimensionPromptValues 提示词片段值列表
     * @return 影响行数
     */
    int batchInsert(@Param("dimensionPromptValues") List<DimensionPromptValue> dimensionPromptValues);
    
    /**
     * 根据ID更新提示词片段值
     *
     * @param dimensionPromptValue 提示词片段值
     * @return 影响行数
     */
    int updateById(DimensionPromptValue dimensionPromptValue);
    
    /**
     * 根据维度配置ID删除提示词片段值
     *
     * @param dimensionConfigId 维度配置ID
     * @return 影响行数
     */
    int deleteByDimensionConfigId(@Param("dimensionConfigId") Long dimensionConfigId);
    
    /**
     * 根据维度配置ID和提示词片段ID删除提示词片段值
     *
     * @param dimensionConfigId 维度配置ID
     * @param promptFragmentId 提示词片段ID
     * @return 影响行数
     */
