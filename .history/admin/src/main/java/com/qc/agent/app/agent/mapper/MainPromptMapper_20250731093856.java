package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.MainPrompt;

/**
 * 主提示词Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MainPromptMapper {

    /**
     * 根据ID查询
     */
    MainPrompt selectById(@Param("id") Long id);

    /**
     * 根据配置ID和类型查询
     */
    @Select("SELECT * FROM qc_ai_main_prompt WHERE config_id = #{configId} AND prompt_type = #{promptType} AND status = '1'")
    MainPrompt selectByConfigIdAndType(@Param("configId") Long configId, @Param("promptType") String promptType);

    @Select("SELECT * FROM qc_ai_main_prompt WHERE config_id = #{configId} AND prompt_type = #{promptType} AND status = '1'")
    MainPrompt selectByConfigIdAndType(@Param("configId") String configId, @Param("promptType") String promptType);

    /**
     * 根据配置ID查询所有提示词
     */
    List<MainPrompt> selectByConfigId(@Param("configId") Long configId);

    /**
     * 插入
     */
    int insert(MainPrompt mainPrompt);

    /**
     * 更新
     */
    int updateById(MainPrompt mainPrompt);

    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据配置ID和类型删除
     */
    int deleteByConfigIdAndType(@Param("configId") Long configId, @Param("promptType") String promptType);
}