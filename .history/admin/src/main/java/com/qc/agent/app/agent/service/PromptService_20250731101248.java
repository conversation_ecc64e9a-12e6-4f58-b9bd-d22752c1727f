package com.qc.agent.app.agent.service;

import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import com.qc.agent.app.agent.model.entity.PromptFragment;

/**
 * 提示词服务接口
 */
public interface PromptService {

    /**
     * 根据配置ID和提示词类型获取主提示词
     *
     * @param configId   配置ID
     * @param promptType 提示词类型
     * @return 主提示词DTO
     */
    MainPromptDTO getMainPromptByConfigIdAndType(Long configId, String promptType);

    /**
     * 根据配置ID（维度编码）和提示词类型获取主提示词
     *
     * @param configId   配置ID（维度编码）
     * @param promptType 提示词类型
     * @return 主提示词DTO
     */
    MainPromptDTO getMainPromptByConfigIdAndType(String configId, String promptType);

    /**
     * 保存主提示词
     *
     * @param mainPromptDTO 主提示词DTO
     * @return 是否保存成功
     */
    boolean saveMainPrompt(MainPromptDTO mainPromptDTO);

    /**
     * 根据主提示词ID获取片段列表
     *
     * @param mainPromptId 主提示词ID
     * @return 片段列表
     */
    List<PromptFragment> getFragmentsByMainPromptId(Long mainPromptId);

    /**
     * 将片段列表转换为JSON对象
     *
     * @param fragments 片段列表
     * @return JSON对象
     */
    JsonNode fragmentsToJsonObject(List<PromptFragment> fragments);

    /**
     * 将JSON对象转换为片段列表
     *
     * @param jsonNode     JSON对象
     * @param mainPromptId 主提示词ID
     * @return 片段列表
     */
    List<PromptFragment> jsonObjectToFragments(JsonNode jsonNode, Long mainPromptId);

    /**
     * 从片段列表生成提示词字符串
     *
     * @param fragments 片段列表
     * @return 提示词字符串
     */
    String generatePromptFromFragments(List<PromptFragment> fragments);

    /**
     * 根据维度配置ID查询提示词片段值列表
     *
     * @param dimensionConfigId 维度配置ID
     * @return 提示词片段值列表
     */
    List<DimensionPromptValue> getPromptValuesByDimensionConfigId(Long dimensionConfigId);

    /**
     * 保存提示词片段值
     *
     * @param dimensionPromptValue 提示词片段值
     * @return 是否保存成功
     */
    boolean savePromptValue(DimensionPromptValue dimensionPromptValue);

    /**
     * 批量保存提示词片段值
     *
     * @param dimensionPromptValues 提示词片段值列表
     * @return 是否保存成功
     */
    boolean savePromptValues(List<DimensionPromptValue> dimensionPromptValues);

    /**
     * 批量导入提示词
     */
    void batchImportPrompts(List<DimensionPromptBO> dimensionPrompts);
}