package com.qc.agent.app.agent.model.entity;

import java.io.Serializable;

import lombok.Data;

/**
 * 主提示词实体类
 */
@Data
public class MainPrompt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主提示词ID，主键
     */
    private Long id;

    /**
     * 逻辑删除状态：1-有效，0-无效
     */
    private String status;

    /**
     * 提示词名称
     */
    private String promptName;

    /**
     * 提示词类型：DIMENSION/SUMMARY_COMPREHENSIVE/SUMMARY_ADVICE
     */
    private String promptType;

    /**
     * 关联qc_ai_summary_config或者qc_ai_dimension_config的id
     */
    private Long configId;
}