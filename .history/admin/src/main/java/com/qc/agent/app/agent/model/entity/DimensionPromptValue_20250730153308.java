package com.qc.agent.app.agent.model.entity;

import lombok.Data;

/**
 * 维度提示词片段值实体类
 */
@Data
public class DimensionPromptValue {
    /**
     * 维度提示词片段值ID，主键
     */
    private Long id;

    /**
     * 逻辑删除状态：1-有效，0-无效
     */
    private String status;

    /**
     * 维度配置ID，关联qc_ai_dimension_config表
     */
    private Long dimensionConfigId;

    /**
     * 提示词片段ID，关联qc_ai_prompt_fragment表
     */
    private Long promptFragmentId;

    /**
     * 片段值
     */
    private String fragmentValue;
