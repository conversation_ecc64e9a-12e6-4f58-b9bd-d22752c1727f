package com.qc.agent.app.agent.mapper;

import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维度提示词片段值Mapper接口
 */
public interface DimensionPromptValueMapper {
    
    /**
     * 根据维度配置ID查询提示词片段值列表
     *
     * @param dimensionConfigId 维度配置ID
     * @return 提示词片段值列表
     */
    List<DimensionPromptValue> selectByDimensionConfigId(@Param("dimensionConfigId") Long dimensionConfigId);
    
    /**
