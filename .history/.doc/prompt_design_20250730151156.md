# 客户洞察助手提示词存储结构设计文档

## 1. 设计目标

1. 在 `available-options` 接口中返回维度选择时，同时返回该维度对应的提示词结构。
2. 当创建新的维度配置时，在选择维度后，能够从 `available-options` 中获取对应的该维度的提示词结构。
3. 如果配置了多个相同的维度，即使结构相同，提示词片段的值可能不同，所以提示词片段的值还需要保存在配置的维度上。
4. 每个提示词都单独维护数据，避免引入复杂性。
5. 在保存维度配置时，将提示词的结构和值保存下来，并能够支持查询接口的回显。

## 2. 表结构设计

### 2.1 现有表结构

- `qc_ai_main_prompt`: 主提示词表
- `qc_ai_prompt_fragment`: 提示词片段表
- `qc_ai_dimension_config`: 维度配置表
- `qc_ai_data_source`: 数据源表

### 2.2 新增表结构

#### 2.2.1 维度默认提示词表

创建 `qc_ai_dimension_default_prompt` 表，用于存储维度代码级别的默认提示词结构：

```sql
CREATE TABLE qc_ai_dimension_default_prompt (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    prompt_name VARCHAR(100) NOT NULL,
    dimension_code VARCHAR(20) NOT NULL
);
COMMENT ON TABLE qc_ai_dimension_default_prompt IS '维度默认提示词表';
COMMENT ON COLUMN qc_ai_dimension_default_prompt.id IS '维度默认提示词ID，主键';
COMMENT ON COLUMN qc_ai_dimension_default_prompt.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_dimension_default_prompt.prompt_name IS '提示词名称';
COMMENT ON COLUMN qc_ai_dimension_default_prompt.dimension_code IS '关联维度代码';
```

#### 2.2.2 维度默认提示词片段表

创建 `qc_ai_dimension_default_prompt_fragment` 表，用于存储维度代码级别的默认提示词片段结构：

```sql
CREATE TABLE qc_ai_dimension_default_prompt_fragment (
    id BIGSERIAL PRIMARY KEY,
    status CHAR(1) DEFAULT '1',
    default_main_prompt_id BIGINT NOT NULL,
    fragment_key VARCHAR(100) NOT NULL,
    fragment_value TEXT NOT NULL,
    sort_order INTEGER DEFAULT 0
);
COMMENT ON TABLE qc_ai_dimension_default_prompt_fragment IS '维度默认提示词片段表';
COMMENT ON COLUMN qc_ai_dimension_default_prompt_fragment.id IS '维度默认提示词片段ID，主键';
COMMENT ON COLUMN qc_ai_dimension_default_prompt_fragment.status IS '逻辑删除状态：1-有效，0-无效';
COMMENT ON COLUMN qc_ai_dimension_default_prompt_fragment.default_main_prompt_id IS '关联默认主提示词ID';
COMMENT ON COLUMN qc_ai_dimension_default_prompt_fragment.fragment_key IS '片段键名';
COMMENT ON COLUMN qc_ai_dimension_default_prompt_fragment.fragment_value IS '片段值';
COMMENT ON COLUMN qc_ai_dimension_default_prompt_fragment.sort_order IS '排序号';
```

## 3. 流程设计

### 3.1 初始化默认提示词结构

在 `getAvailableOptions` 方法中：

1. 查询所有数据源，按维度代码分组。
2. 为每个维度代码创建默认提示词结构（如果还没有的话）：
   - 在 `qc_ai_dimension_default_prompt` 表中创建默认主提示词记录。
   - 在 `qc_ai_dimension_default_prompt_fragment` 表中创建默认提示词片段记录，片段值为空。

### 3.2 创建新的维度配置

在 `createDimensionConfig` 方法中：

1. 根据维度代码查询 `qc_ai_dimension_default_prompt` 表，获取默认提示词 ID。
2. 复制默认提示词结构作为新配置的提示词结构：
   - 在 `qc_ai_main_prompt` 表中创建新的主提示词记录，关联到新的维度配置 ID。
   - 复制 `qc_ai_dimension_default_prompt_fragment` 表中的片段结构到 `qc_ai_prompt_fragment` 表中，但片段值为空。

### 3.3 保存维度配置

在 `saveWorkspace` 方法中：

1. 调用 `PromptService.saveMainPrompt` 方法保存提示词的结构和值。
2. 该方法会根据配置 ID 和提示词类型查询是否已存在相同配置 ID 和类型的主提示词。
3. 如果不存在则新增主提示词，如果存在则更新主提示词。
4. 如果 DTO 中包含片段，则先删除现有片段，然后保存新片段。

### 3.4 查询维度配置

在 `getWorkspace` 方法中：

1. 调用 `PromptService.getMainPromptByConfigIdAndType` 方法回显提示词的结构和值。
2. 该方法会根据配置 ID 和提示词类型查询主提示词及其片段。

## 4. 代码实现建议

### 4.1 修改 `getAvailableOptions` 方法

在 `getAvailableOptions` 方法中，为每个维度代码创建默认提示词结构：

```java
@Override
public InsightConfigWorkspaceVO.AvailableOptions getAvailableOptions() {
    InsightConfigWorkspaceVO.AvailableOptions options = new InsightConfigWorkspaceVO.AvailableOptions();
    // 按维度分组数据源
    List<InsightDataSource> allDataSources = insightDataSourceMapper.selectAll();
    Map<String, List<InsightDataSource>> dataSourcesByDimension = allDataSources.stream()
            .collect(Collectors.groupingBy(InsightDataSource::getBelongDimensionCode));
    // 构建维度下的数据源结构
    List<InsightDimensionConfigVO> availableDimensionVOs = new ArrayList<>();
    for (Map.Entry<String, List<InsightDataSource>> entry : dataSourcesByDimension.entrySet()) {
        String dimensionCode = entry.getKey();
        List<InsightDataSource> dataSources = entry.getValue();
        InsightDimensionConfigVO dimensionVO = new InsightDimensionConfigVO();
        dimensionVO.setDimensionCode(dimensionCode);
        dimensionVO.setDimensionName(dataSources.get(0).getBelongDimensionName());

        // 为每个维度代码创建默认提示词结构（如果还没有的话）
        createDefaultPromptIfNotExists(dimensionCode);

        List<InsightDataSourceVO> dataSourceVOs = dataSources.stream().map(dataSource -> {
            InsightDataSourceVO dsVO = new InsightDataSourceVO();
            BeanUtils.copyProperties(dataSource, dsVO);
            List<InsightDataItem> items = insightDataItemMapper
                    .selectByQueryBusinessCode(dataSource.getSourceCode());
            List<InsightDataItem> treeItems = InsightDataItemTreeUtils.buildTree(items);
            Map<String, List<InsightDataItem>> itemsByTypeMap = treeItems.stream()
                    .collect(Collectors.groupingBy(InsightDataItem::getDataTypeCode));
            List<InsightDataItemGroup> itemsByType = itemsByTypeMap.entrySet().stream()
                    .map(mapEntry -> {
                        List<InsightDataItem> copiedItems = mapEntry.getValue().stream()
                                .map(item -> {
                                    InsightDataItem copiedItem = new InsightDataItem();
                                    BeanUtils.copyProperties(item, copiedItem);
                                    copiedItem.setChildren(null);
                                    return copiedItem;
                                })
                                .collect(Collectors.toList());
                        return new InsightDataItemGroup(mapEntry.getKey(), null, copiedItems);
                    })
                    .collect(Collectors.toList());
            dsVO.setDataItemsByType(itemsByType);
            return dsVO;
        }).collect(Collectors.toList());
        dimensionVO.setDataSources(dataSourceVOs);
        availableDimensionVOs.add(dimensionVO);
    }
    options.setDimensions(availableDimensionVOs);
    return options;
}

private void createDefaultPromptIfNotExists(String dimensionCode) {
    // 查询是否已存在该维度代码的默认提示词
    DimensionDefaultPrompt existingDefaultPrompt = dimensionDefaultPromptMapper.selectByDimensionCode(dimensionCode);
    if (existingDefaultPrompt == null) {
        // 创建默认主提示词
        DimensionDefaultPrompt defaultMainPrompt = new DimensionDefaultPrompt();
        defaultMainPrompt.setPromptName(dimensionCode + "默认提示词");
        defaultMainPrompt.setDimensionCode(dimensionCode);
        defaultMainPrompt.setStatus("1");
        dimensionDefaultPromptMapper.insert(defaultMainPrompt);

        // 创建默认提示词片段（片段值为空）
        List<DimensionDefaultPromptFragment> defaultFragments = createDefaultPromptFragments(defaultMainPrompt.getId(), dimensionCode);
        dimensionDefaultPromptFragmentMapper.batchInsert(defaultFragments);
    }
}

private List<DimensionDefaultPromptFragment> createDefaultPromptFragments(Long defaultMainPromptId, String dimensionCode) {
    List<DimensionDefaultPromptFragment> fragments = new ArrayList<>();
    // 根据维度代码创建默认的提示词片段结构
    // 这里需要根据具体的业务需求来定义默认的片段结构
    // 片段值初始化为空字符串
    // 示例：
    DimensionDefaultPromptFragment fragment1 = new DimensionDefaultPromptFragment();
    fragment1.setDefaultMainPromptId(defaultMainPromptId);
    fragment1.setFragmentKey("role");
    fragment1.setFragmentValue("");
    fragment1.setSortOrder(1);
    fragment1.setStatus("1");
    fragments.add(fragment1);

    DimensionDefaultPromptFragment fragment2 = new DimensionDefaultPromptFragment();
    fragment2.setDefaultMainPromptId(defaultMainPromptId);
    fragment2.setFragmentKey("data");
    fragment2.setFragmentValue("");
    fragment2.setSortOrder(2);
    fragment2.setStatus("1");
    fragments.add(fragment2);

    return fragments;
}
```

### 4.2 修改 `createDimensionConfig` 方法

在 `createDimensionConfig` 方法中，复制默认提示词结构：

```java
/**
 * 创建维度配置
 */
private void createDimensionConfig(InsightDimensionConfigDTO dto, Long agentId) {
    TenantUser currentUser = UserManager.getTenantUser();
    Long currentUserId = currentUser.getUserId();
    String currentUserName = currentUser.getUserName();
    InsightDimensionConfig dimensionConfig = new InsightDimensionConfig();
    BeanUtils.copyProperties(dto, dimensionConfig);
    dimensionConfig.setAgentId(agentId);

    // 自动生成维度编码和名称（如果前端没有传递）
    if (dimensionConfig.getDimensionCode() == null) {
        // 根据数据项自动推导维度编码
        String dimensionCode = deriveDimensionCodeFromDataItems(dto.getDataSources());
        dimensionConfig.setDimensionCode(dimensionCode);
    }

    if (dimensionConfig.getDimensionName() == null) {
        // 根据维度编码生成维度名称
        String dimensionName = deriveDimensionNameFromCode(dimensionConfig.getDimensionCode());
        dimensionConfig.setDimensionName(dimensionName);
    }

    Timestamp now = new Timestamp(System.currentTimeMillis());
    dimensionConfig.setStatus("1");
    dimensionConfig.setCreatorId(currentUserId);
    dimensionConfig.setCreatorName(currentUserName);
    dimensionConfig.setCreateTime(now);
    dimensionConfig.setModifyierId(currentUserId);
    dimensionConfig.setModifyierName(currentUserName);
    dimensionConfig.setModifyTime(now);
    insightDimensionConfigMapper.insert(dimensionConfig);
    Long newDimensionConfigId = dimensionConfig.getId();

    // 复制默认提示词结构
    copyDefaultPromptStructure(dimensionConfig.getDimensionCode(), newDimensionConfigId);

    saveDimensionDataItemRelations(newDimensionConfigId, dto.getDataSources());
    saveDimensionStandardRelations(newDimensionConfigId, dto.getStandards(), CONFIG_TYPE_DIMENSION);
}

private void copyDefaultPromptStructure(String dimensionCode, Long newDimensionConfigId) {
    // 查询该维度代码的默认提示词
    DimensionDefaultPrompt defaultMainPrompt = dimensionDefaultPromptMapper.selectByDimensionCode(dimensionCode);
    if (defaultMainPrompt != null) {
        // 查询默认提示词片段
        List<DimensionDefaultPromptFragment> defaultFragments = dimensionDefaultPromptFragmentMapper.selectByDefaultMainPromptId(defaultMainPrompt.getId());

        // 创建新的主提示词记录
        MainPrompt newMainPrompt = new MainPrompt();
        newMainPrompt.setPromptName(defaultMainPrompt.getPromptName());
        newMainPrompt.setPromptType("DIMENSION");
        newMainPrompt.setConfigId(newDimensionConfigId);
        newMainPrompt.setStatus("1");
        mainPromptMapper.insert(newMainPrompt);

        // 复制提示词片段结构到qc_ai_prompt_fragment表中（片段值为空）
        List<PromptFragment> newFragments = new ArrayList<>();
        for (DimensionDefaultPromptFragment defaultFragment : defaultFragments) {
            PromptFragment newFragment = new PromptFragment();
            newFragment.setMainPromptId(newMainPrompt.getId());
            newFragment.setFragmentKey(defaultFragment.getFragmentKey());
            newFragment.setFragmentValue(""); // 片段值为空
            newFragment.setSortOrder(defaultFragment.getSortOrder());
            newFragment.setStatus("1");
            newFragments.add(newFragment);
        }
        promptFragmentMapper.batchInsert(newFragments);
    }
}
```

### 4.3 保持现有的 `PromptService` 实现

现有的 `PromptService` 实现已经能够满足需求，无需修改。

## 5. 总结

通过以上设计，我们能够：

1. 在 `available-options` 接口中返回维度代码级别的默认提示词结构。
2. 当创建新的维度配置时，复制默认提示词结构作为新配置的提示词结构，但提示词片段的值为空。
3. 当保存维度配置时，将提示词的结构和值保存到 `qc_ai_main_prompt` 和 `qc_ai_prompt_fragment` 表中。
4. 在查询维度配置时，能够回显提示词的结构和值。
5. 每个维度配置都有唯一的 ID，不会出现提示词值覆盖的问题。
6. 默认提示词结构和具体配置实例的提示词结构完全分离，避免了相互影响。
