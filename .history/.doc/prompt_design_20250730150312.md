# 客户洞察助手提示词存储结构设计文档

## 1. 设计目标

1. 在 `available-options` 接口中返回维度选择时，同时返回该维度对应的提示词结构。
2. 当创建新的维度配置时，在选择维度后，能够从 `available-options` 中获取对应的该维度的提示词结构。
3. 如果配置了多个相同的维度，即使结构相同，提示词片段的值可能不同，所以提示词片段的值还需要保存在配置的维度上。
4. 每个提示词都单独维护数据，避免引入复杂性。
5. 在保存维度配置时，将提示词的结构和值保存下来，并能够支持查询接口的回显。

## 2. 表结构设计

### 2.1 现有表结构

- `qc_ai_main_prompt`: 主提示词表
- `qc_ai_prompt_fragment`: 提示词片段表
- `qc_ai_dimension_config`: 维度配置表
- `qc_ai_data_source`: 数据源表

### 2.2 新增字段

在 `qc_ai_data_source` 表中添加以下字段：

- `default_main_prompt_id`: 默认主提示词 ID，关联 `qc_ai_main_prompt` 表

## 3. 流程设计

### 3.1 初始化默认提示词结构

在 `getAvailableOptions` 方法中：

1. 查询所有数据源，按维度代码分组。
2. 为每个维度代码创建默认提示词结构（如果还没有的话）：
   - 在 `qc_ai_main_prompt` 表中创建主提示词记录。
   - 在 `qc_ai_prompt_fragment` 表中创建提示词片段记录，片段值为空。
   - 将主提示词 ID 存储在 `qc_ai_data_source` 表的 `default_main_prompt_id` 字段中。

### 3.2 创建新的维度配置

在 `createDimensionConfig` 方法中：

1. 根据维度代码查询 `qc_ai_data_source` 表，获取 `default_main_prompt_id`。
2. 复制默认提示词结构作为新配置的提示词结构：
   - 在 `qc_ai_main_prompt` 表中创建新的主提示词记录，关联到新的维度配置 ID。
   - 复制 `qc_ai_prompt_fragment` 表中的片段结构，但片段值为空。

### 3.3 保存维度配置

在 `saveWorkspace` 方法中：

1. 调用 `PromptService.saveMainPrompt` 方法保存提示词的结构和值。
2. 该方法会根据配置 ID 和提示词类型查询是否已存在相同配置 ID 和类型的主提示词。
3. 如果不存在则新增主提示词，如果存在则更新主提示词。
4. 如果 DTO 中包含片段，则先删除现有片段，然后保存新片段。

### 3.4 查询维度配置

在 `getWorkspace` 方法中：

1. 调用 `PromptService.getMainPromptByConfigIdAndType` 方法回显提示词的结构和值。
2. 该方法会根据配置 ID 和提示词类型查询主提示词及其片段。

## 4. 代码实现建议

### 4.1 修改 `getAvailableOptions` 方法

在 `getAvailableOptions` 方法中，为每个维度代码创建默认提示词结构：

```java
@Override
public InsightConfigWorkspaceVO.AvailableOptions getAvailableOptions() {
    InsightConfigWorkspaceVO.AvailableOptions options = new InsightConfigWorkspaceVO.AvailableOptions();
    // 按维度分组数据源
    List<InsightDataSource> allDataSources = insightDataSourceMapper.selectAll();
    Map<String, List<InsightDataSource>> dataSourcesByDimension = allDataSources.stream()
            .collect(Collectors.groupingBy(InsightDataSource::getBelongDimensionCode));
    // 构建维度下的数据源结构
    List<InsightDimensionConfigVO> availableDimensionVOs = new ArrayList<>();
    for (Map.Entry<String, List<InsightDataSource>> entry : dataSourcesByDimension.entrySet()) {
        String dimensionCode = entry.getKey();
        List<InsightDataSource> dataSources = entry.getValue();
        InsightDimensionConfigVO dimensionVO = new InsightDimensionConfigVO();
        dimensionVO.setDimensionCode(dimensionCode);
        dimensionVO.setDimensionName(dataSources.get(0).getBelongDimensionName());

        // 为每个维度代码创建默认提示词结构（如果还没有的话）
        createDefaultPromptIfNotExists(dimensionCode, dataSources);

        List<InsightDataSourceVO> dataSourceVOs = dataSources.stream().map(dataSource -> {
            InsightDataSourceVO dsVO = new InsightDataSourceVO();
            BeanUtils.copyProperties(dataSource, dsVO);
            List<InsightDataItem> items = insightDataItemMapper
                    .selectByQueryBusinessCode(dataSource.getSourceCode());
            List<InsightDataItem> treeItems = InsightDataItemTreeUtils.buildTree(items);
            Map<String, List<InsightDataItem>> itemsByTypeMap = treeItems.stream()
                    .collect(Collectors.groupingBy(InsightDataItem::getDataTypeCode));
            List<InsightDataItemGroup> itemsByType = itemsByTypeMap.entrySet().stream()
                    .map(mapEntry -> {
                        List<InsightDataItem> copiedItems = mapEntry.getValue().stream()
                                .map(item -> {
```
