<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qc.agent.app.agent.mapper.DimensionPromptValueMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="dimension_config_id" property="dimensionConfigId"/>
        <result column="prompt_fragment_id" property="promptFragmentId"/>
        <result column="fragment_value" property="fragmentValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, status, dimension_config_id, prompt_fragment_id, fragment_value
    </sql>

    <!-- 根据维度配置ID查询提示词片段值列表 -->
    <select id="selectByDimensionConfigId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_prompt_value
        WHERE dimension_config_id = #{dimensionConfigId}
        AND status = '1'
        ORDER BY id
    </select>

    <!-- 根据维度配置ID和提示词片段ID查询提示词片段值 -->
    <select id="selectByDimensionConfigIdAndPromptFragmentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM qc_ai_dimension_prompt_value
        WHERE dimension_config_id = #{dimensionConfigId}
        AND prompt_fragment_id = #{promptFragmentId}
        AND status = '1'
        LIMIT 1
    </select>

    <!-- 插入提示词片段值 -->
    <insert id="insert" parameterType="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        INSERT INTO qc_ai_dimension_prompt_value (status, dimension_config_id, prompt_fragment_id, fragment_value)
        VALUES (#{status}, #{dimensionConfigId}, #{promptFragmentId}, #{fragmentValue})
    </insert>

    <!-- 批量插入提示词片段值 -->
    <insert id="batchInsert">
        INSERT INTO qc_ai_dimension_prompt_value (status, dimension_config_id, prompt_fragment_id, fragment_value)
        VALUES
        <foreach collection="dimensionPromptValues" item="item" separator=",">
            (#{item.status}, #{item.dimensionConfigId}, #{item.promptFragmentId}, #{item.fragmentValue})
        </foreach>
    </insert>

    <!-- 根据ID更新提示词片段值 -->
    <update id="updateById" parameterType="com.qc.agent.app.agent.model.entity.DimensionPromptValue">
        UPDATE qc_ai_dimension_prompt_value
        SET status = #{status},
        dimension_config_id = #{dimensionConfigId},
        prompt_fragment_id = #{promptFragmentId},
        fragment_value = #{fragmentValue}
        WHERE id = #{id}
    </update>

    <!-- 根据维度配置ID删除提示词片段值 -->
    <update id="deleteByDimensionConfigId">
        UPDATE qc_ai_dimension_prompt_value
        SET status = '0'
        WHERE dimension_config_id = #{dimensionConfigId}
    </update>

    <!-- 根据维度配置ID和提示词片段ID删除提示词片段值 -->
    <update id="deleteByDimensionConfigIdAndPromptFragmentId">
        UPDATE qc_ai_dimension_prompt_value
        SET status = '0'
        WHERE dimension_config_id = #{dimensionConfigId}
        AND prompt_fragment_id = #{promptFragmentId}
    </update>

</mapper>