package com.qc.agent.app.agent.service.impl;

import lombok.extern.slf4j.Slf4j;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.register.service.TenantRegisterHook;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;

@Slf4j
@Component
public class CustomerInsightModeHook implements TenantRegisterHook {
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    @Resource
    private CustomerInsightConfigService customerInsightConfigService;

    @Override
    public void afterRegister(DatasourceConfig config) {
        try {
            Long tenantId = config.getId();
            String redisKey = "appsvr:sysparm:cache:custom:" + tenantId;

            // 检查是否已存在配置
            if (customerInsightConfigService.checkInsightModeConfig(config)) {
                log.info("租户{}已存在客户洞察模式配置，跳过初始化", tenantId);
                return;
            }

            // 从Redis获取客户洞察模式配置
            String insightMode = (String) redisTemplate.opsForValue().get(redisKey);

            if (insightMode != null) {
                // 保存客户洞察模式配置到数据库
                customerInsightConfigService.saveInsightModeConfig(config, insightMode);
                log.info("租户{}的客户洞察模式配置初始化成功，模式: {}", tenantId, insightMode);
            } else {
                log.warn("租户{}未找到客户洞察模式配置，跳过初始化", tenantId);
            }
        } catch (Exception e) {
            log.error("租户{}客户洞察模式配置初始化失败", config.getId(), e);
        }
    }
}