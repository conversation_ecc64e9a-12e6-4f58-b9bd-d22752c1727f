package com.qc.agent.app.agent.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import com.qc.agent.app.agent.model.entity.MainPrompt;

/**
 * 主提示词Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MainPromptMapper {

    /**
     * 根据ID查询
     */
    MainPrompt selectById(@Param("id") Long id);

    /**
     * 根据维度编码和类型查询
     */
    @Select("SELECT * FROM qc_ai_main_prompt WHERE dimension_code = #{dimensionCode} AND prompt_type = #{promptType} AND status = '1'")
    MainPrompt selectByConfigIdAndType(@Param("dimensionCode") Long dimensionCode,
            @Param("promptType") String promptType);

    @Select("SELECT * FROM qc_ai_main_prompt WHERE dimension_code = #{dimensionCode} AND prompt_type = #{promptType} AND status = '1'")
    MainPrompt selectByConfigIdAndType(@Param("dimensionCode") String dimensionCode,
            @Param("promptType") String promptType);

    /**
     * 根据维度编码查询所有提示词
     */
    List<MainPrompt> selectByConfigId(@Param("dimensionCode") Long dimensionCode);

    /**
     * 插入
     */
    int insert(MainPrompt mainPrompt);

    /**
     * 更新
     */
    int updateById(MainPrompt mainPrompt);

    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据维度编码和类型删除
     */
    int deleteByConfigIdAndType(@Param("dimensionCode") Long dimensionCode, @Param("promptType") String promptType);
}