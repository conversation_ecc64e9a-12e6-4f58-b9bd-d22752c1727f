package com.qc.agent.app.agent.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.qc.agent.app.agent.mapper.DimensionPromptValueMapper;
import com.qc.agent.app.agent.model.entity.DimensionPromptValue;
import com.qc.agent.app.agent.service.DimensionPromptValueService;

import lombok.extern.slf4j.Slf4j;

/**
 * 维度提示词片段值服务实现类
 */
@Slf4j
@Service
public class DimensionPromptValueServiceImpl implements DimensionPromptValueService {

    @Resource
    private DimensionPromptValueMapper dimensionPromptValueMapper;

    @Override
    public List<DimensionPromptValue> getPromptValuesByDimensionConfigId(Long dimensionConfigId) {
        if (dimensionConfigId == null) {
            return null;
        }
        return dimensionPromptValueMapper.selectByDimensionConfigId(dimensionConfigId);
    }

    @Override
    public DimensionPromptValue getPromptValueByDimensionConfigIdAndPromptFragmentId(Long dimensionConfigId,
            Long promptFragmentId) {
        if (dimensionConfigId == null || promptFragmentId == null) {
            return null;
        }
        return dimensionPromptValueMapper.selectByDimensionConfigIdAndPromptFragmentId(dimensionConfigId,
                promptFragmentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePromptValue(DimensionPromptValue dimensionPromptValue) {
        if (dimensionPromptValue == null) {
            return false;
        }

        // 检查是否已存在相同的维度配置ID和提示词片段ID的记录
        DimensionPromptValue existingPromptValue = dimensionPromptValueMapper
                .selectByDimensionConfigIdAndPromptFragmentId(
                        dimensionPromptValue.getDimensionConfigId(), dimensionPromptValue.getPromptFragmentId());

        if (existingPromptValue == null) {
            // 新增
            dimensionPromptValue.setStatus("1");
            return dimensionPromptValueMapper.insert(dimensionPromptValue) > 0;
        } else {
            // 更新
            dimensionPromptValue.setId(existingPromptValue.getId());
            return dimensionPromptValueMapper.updateById(dimensionPromptValue) > 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePromptValues(List<DimensionPromptValue> dimensionPromptValues) {
        if (CollectionUtils.isEmpty(dimensionPromptValues)) {
            return false;
        }

        // 先删除现有的提示词片段值
        Long dimensionConfigId = dimensionPromptValues.get(0).getDimensionConfigId();
        if (dimensionConfigId != null) {
            dimensionPromptValueMapper.deleteByDimensionConfigId(dimensionConfigId);
        }

        // 批量插入新的提示词片段值
        for (DimensionPromptValue dimensionPromptValue : dimensionPromptValues) {
            dimensionPromptValue.setStatus("1");
        }
        return dimensionPromptValueMapper.batchInsert(dimensionPromptValues) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePromptValuesByDimensionConfigId(Long dimensionConfigId) {
        if (dimensionConfigId == null) {
            return false;
        }
        return dimensionPromptValueMapper.deleteByDimensionConfigId(dimensionConfigId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePromptValueByDimensionConfigIdAndPromptFragmentId(Long dimensionConfigId,
            Long promptFragmentId) {
        if (dimensionConfigId == null || promptFragmentId == null) {
            return false;
        }
        return dimensionPromptValueMapper.deleteByDimensionConfigIdAndPromptFragmentId(dimensionConfigId,
                promptFragmentId) > 0;
    }
}