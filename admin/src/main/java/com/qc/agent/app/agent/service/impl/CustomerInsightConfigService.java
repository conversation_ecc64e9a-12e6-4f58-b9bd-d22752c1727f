package com.qc.agent.app.agent.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.datasource.util.JdbcConnectionBuilder;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

@Slf4j
@Service
public class CustomerInsightConfigService {
    
    /**
     * 初始化客户洞察模式配置
     */
    public void initInsightModeConfig(DatasourceConfig config, String insightMode) {
        String sql = "INSERT INTO qc_ai_agent_ext_config (agent_id, config_key, config_value, create_time) " +
                    "VALUES (?, ?, ?, CURRENT_TIMESTAMP) " +
                    "ON CONFLICT (agent_id, config_key) DO UPDATE SET " +
                    "config_value = EXCLUDED.config_value, modify_time = CURRENT_TIMESTAMP";
        
        try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            // 客户洞察助手的agent_id，根据实际情况调整
            Long customerInsightAgentId = 8L; // 或者从常量中获取
            
            stmt.setLong(1, customerInsightAgentId);
            stmt.setString(2, "insight_mode");
            stmt.setString(3, insightMode);
            
            int result = stmt.executeUpdate();
            log.info("客户洞察模式配置插入结果: {}, 模式: {}", result, insightMode);
            
        } catch (SQLException e) {
            throw new RuntimeException("插入客户洞察模式配置失败", e);
        }
    }
}