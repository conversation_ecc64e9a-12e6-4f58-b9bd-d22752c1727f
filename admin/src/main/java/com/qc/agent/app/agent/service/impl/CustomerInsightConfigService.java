package com.qc.agent.app.agent.service.impl;

import com.qc.agent.jdbc.datasource.connection.JdbcConnectionBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@Slf4j
@Service
public class CustomerInsightConfigService {

    /**
     * 客户洞察助手的agent_id
     */
    private static final Long CUSTOMER_INSIGHT_AGENT_ID = 8L;

    /**
     * 洞察模式配置键
     */
    private static final String INSIGHT_MODE_CONFIG_KEY = "insight_mode";

    /**
     * 客户模式值
     */
    private static final String CUSTOMER_MODE = "0";

    /**
     * 终端经销商模式值
     */
    private static final String DEALER_MODE = "1";

    /**
     * 检查是否已存在洞察模式配置
     *
     * @param config 数据源配置
     * @return 是否已存在配置
     */
    public boolean checkInsightModeConfig(DatasourceConfig config) {
        String sql = "SELECT COUNT(*) FROM qc_ai_agent_ext_config WHERE agent_id = ? AND config_key = ? AND status = '1'";

        try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, CUSTOMER_INSIGHT_AGENT_ID);
            stmt.setString(2, INSIGHT_MODE_CONFIG_KEY);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }

        } catch (SQLException e) {
            log.error("检查客户洞察模式配置失败", e);
        }

        return false;
    }

    /**
     * 保存洞察模式配置
     *
     * @param config 数据源配置
     * @param insightMode 洞察模式
     */
    public void saveInsightModeConfig(DatasourceConfig config, String insightMode) {
        String convertedMode = convertInsightMode(insightMode);

        String sql = "INSERT INTO qc_ai_agent_ext_config (agent_id, config_key, config_value, description, status, create_time) " +
                    "VALUES (?, ?, ?, ?, '1', CURRENT_TIMESTAMP) " +
                    "ON CONFLICT (agent_id, config_key) DO UPDATE SET " +
                    "config_value = EXCLUDED.config_value, modify_time = CURRENT_TIMESTAMP";

        try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, CUSTOMER_INSIGHT_AGENT_ID);
            stmt.setString(2, INSIGHT_MODE_CONFIG_KEY);
            stmt.setString(3, convertedMode);
            stmt.setString(4, "客户洞察模式配置：0-客户模式，1-终端经销商模式");

            int result = stmt.executeUpdate();
            log.info("客户洞察模式配置保存成功: {}, 原始模式: {}, 转换后模式: {}", result, insightMode, convertedMode);

        } catch (SQLException e) {
            throw new RuntimeException("保存客户洞察模式配置失败", e);
        }
    }

    /**
     * 转换Redis值到数据库值
     *
     * @param redisValue Redis中的值
     * @return 数据库中的值（0或1）
     */
    public String convertInsightMode(String redisValue) {
        if (redisValue == null || redisValue.trim().isEmpty()) {
            log.warn("Redis值为空，默认使用客户模式");
            return CUSTOMER_MODE;
        }

        String value = redisValue.trim().toLowerCase();

        // 处理数字形式
        if ("0".equals(value) || "1".equals(value)) {
            return value;
        }

        // 处理英文形式
        if ("customer".equals(value) || "client".equals(value)) {
            return CUSTOMER_MODE;
        }
        if ("dealer".equals(value) || "terminal".equals(value) || "distributor".equals(value)) {
            return DEALER_MODE;
        }

        // 处理中文形式
        if (value.contains("客户") || value.contains("客戶")) {
            return CUSTOMER_MODE;
        }
        if (value.contains("终端") || value.contains("經銷") || value.contains("经销") || value.contains("代理")) {
            return DEALER_MODE;
        }

        // 默认返回客户模式
        log.warn("无法识别的洞察模式值: {}, 默认使用客户模式", redisValue);
        return CUSTOMER_MODE;
    }
}