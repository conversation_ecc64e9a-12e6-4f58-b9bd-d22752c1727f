package com.qc.agent.app.agent.service.impl;

import com.qc.agent.jdbc.datasource.connection.JdbcConnectionBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.beans.factory.annotation.Value;
import com.qc.agent.platform.datasource.model.DatasourceConfig;
import com.qc.agent.platform.util.HttpUtil;
import com.qc.agent.platform.register.UserManager;
import com.qc.agent.platform.datasource.service.DatasourceService;
import com.alibaba.fastjson.JSONObject;
import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CustomerInsightConfigService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private DatasourceService datasourceService;

    @Value("${ai.agent.appsvr-domin-url}")
    private String appsvrDominUrl;

    /**
     * 客户洞察助手的agent_id
     */
    private static final Long CUSTOMER_INSIGHT_AGENT_ID = 8L;

    /**
     * 洞察模式配置键
     */
    private static final String INSIGHT_MODE_CONFIG_KEY = "insight_mode";

    /**
     * 客户模式值
     */
    private static final String CUSTOMER_MODE = "0";

    /**
     * 终端经销商模式值
     */
    private static final String DEALER_MODE = "1";

    /**
     * Redis key 前缀
     */
    private static final String REDIS_KEY_PREFIX = "qc:agent:insight:mode:";

    /**
     * Redis 缓存过期时间（小时）
     */
    private static final long REDIS_EXPIRE_HOURS = 24;

    /**
     * 检查是否已存在洞察模式配置
     *
     * @param config 数据源配置
     * @return 是否已存在配置
     */
    public boolean checkInsightModeConfig(DatasourceConfig config) {
        String sql = "SELECT COUNT(*) FROM qc_ai_agent_ext_config WHERE agent_id = ? AND config_key = ? AND status = '1'";

        try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, CUSTOMER_INSIGHT_AGENT_ID);
            stmt.setString(2, INSIGHT_MODE_CONFIG_KEY);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }

        } catch (SQLException e) {
            log.error("检查客户洞察模式配置失败", e);
        }

        return false;
    }

    /**
     * 保存洞察模式配置
     *
     * @param config 数据源配置
     * @param insightMode 洞察模式
     */
    public void saveInsightModeConfig(DatasourceConfig config, String insightMode) {
        String convertedMode = convertInsightMode(insightMode);

        String sql = "INSERT INTO qc_ai_agent_ext_config (agent_id, config_key, config_value, description, status, create_time) " +
                    "VALUES (?, ?, ?, ?, '1', CURRENT_TIMESTAMP) " +
                    "ON CONFLICT (agent_id, config_key) DO UPDATE SET " +
                    "config_value = EXCLUDED.config_value, modify_time = CURRENT_TIMESTAMP";

        try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, CUSTOMER_INSIGHT_AGENT_ID);
            stmt.setString(2, INSIGHT_MODE_CONFIG_KEY);
            stmt.setString(3, convertedMode);
            stmt.setString(4, "客户洞察模式配置：0-客户模式，1-终端经销商模式");

            int result = stmt.executeUpdate();
            log.info("客户洞察模式配置保存成功: {}, 原始模式: {}, 转换后模式: {}", result, insightMode, convertedMode);

        } catch (SQLException e) {
            throw new RuntimeException("保存客户洞察模式配置失败", e);
        }
    }

    /**
     * 转换Redis值到数据库值
     *
     * @param redisValue Redis中的值
     * @return 数据库中的值（0或1）
     */
    public String convertInsightMode(String redisValue) {
        if (redisValue == null || redisValue.trim().isEmpty()) {
            log.warn("Redis值为空，默认使用客户模式");
            return CUSTOMER_MODE;
        }

        String value = redisValue.trim();

        // Redis返回值只有"1"或"0"，"1"表示终端经销商模式，其他情况都返回"0"（客户模式）
        if ("1".equals(value)) {
            return DEALER_MODE;
        }

        // 其他所有情况都返回客户模式
        if (!"0".equals(value)) {
            log.warn("Redis值不是标准的0或1: {}, 默认使用客户模式", redisValue);
        }
        return CUSTOMER_MODE;
    }

    /**
     * 初始化客户洞察模式配置（保持向后兼容）
     *
     * @param config 数据源配置
     * @param insightMode 洞察模式
     */
    public void initInsightModeConfig(DatasourceConfig config, String insightMode) {
        saveInsightModeConfig(config, insightMode);
    }

    /**
     * 检查并更新洞察模式
     * 先从Redis获取，如果不存在或与数据库不一致，则调用平台接口更新
     *
     * @param tenantId 租户ID
     * @return 洞察模式（0或1）
     */
    public String checkAndUpdateInsightMode(Long tenantId) {
        try {
            // 1. 从Redis获取缓存的模式
            String redisKey = REDIS_KEY_PREFIX + tenantId;
            String cachedMode = (String) redisTemplate.opsForValue().get(redisKey);

            // 2. 从数据库获取当前模式
            String dbMode = getInsightModeFromDatabase(tenantId);

            // 3. 如果Redis中有缓存且与数据库一致，直接返回
            if (cachedMode != null && cachedMode.equals(dbMode)) {
                log.debug("租户{}的洞察模式从Redis缓存获取: {}", tenantId, cachedMode);
                return cachedMode;
            }

            // 4. 调用平台接口获取最新模式
            String latestMode = callPlatformApiForInsightMode();

            // 5. 如果获取到的模式与数据库不一致，更新数据库和Redis
            if (!latestMode.equals(dbMode)) {
                updateInsightModeToDatabase(tenantId, latestMode);
                log.info("租户{}的洞察模式已更新到数据库: {} -> {}", tenantId, dbMode, latestMode);
            }

            // 6. 更新Redis缓存
            redisTemplate.opsForValue().set(redisKey, latestMode, REDIS_EXPIRE_HOURS, TimeUnit.HOURS);
            log.info("租户{}的洞察模式已更新到Redis缓存: {}", tenantId, latestMode);

            return latestMode;

        } catch (Exception e) {
            log.error("检查并更新租户{}的洞察模式失败", tenantId, e);
            // 出错时返回默认的客户模式
            return CUSTOMER_MODE;
        }
    }

    /**
     * 调用平台接口获取客户模式
     *
     * @return 洞察模式（0或1）
     */
    private String callPlatformApiForInsightMode() {
        try {
            String url = appsvrDominUrl + "/sysapp/quota/getCmModel.do";
            log.info("调用平台接口获取客户模式: {}", url);

            String response = HttpUtil.get(url);
            log.info("平台接口返回结果: {}", response);

            // 解析响应结果
            JSONObject jsonResponse = JSONObject.parseObject(response);
            if (jsonResponse != null && jsonResponse.containsKey("data")) {
                String modeValue = jsonResponse.getString("data");
                return convertInsightMode(modeValue);
            }

            log.warn("平台接口返回格式异常，使用默认客户模式");
            return CUSTOMER_MODE;

        } catch (Exception e) {
            log.error("调用平台接口获取客户模式失败", e);
            return CUSTOMER_MODE;
        }
    }

    /**
     * 从数据库获取洞察模式
     *
     * @param tenantId 租户ID
     * @return 洞察模式（0或1）
     */
    private String getInsightModeFromDatabase(Long tenantId) {
        String sql = "SELECT config_value FROM qc_ai_agent_ext_config WHERE agent_id = ? AND config_key = ? AND status = '1'";

        try {
            // 获取指定租户的数据源配置
            DatasourceConfig config = datasourceService.getDatasourceConfig(tenantId);
            if (config == null) {
                log.warn("租户{}的数据源配置不存在", tenantId);
                return CUSTOMER_MODE;
            }

            try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setLong(1, CUSTOMER_INSIGHT_AGENT_ID);
                stmt.setString(2, INSIGHT_MODE_CONFIG_KEY);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        String configValue = rs.getString("config_value");
                        log.debug("从数据库获取租户{}的洞察模式: {}", tenantId, configValue);
                        return configValue != null ? configValue : CUSTOMER_MODE;
                    }
                }
            }

            // 如果没有找到配置，返回默认的客户模式
            log.debug("租户{}未找到洞察模式配置，返回默认客户模式", tenantId);
            return CUSTOMER_MODE;

        } catch (Exception e) {
            log.error("从数据库获取租户{}的洞察模式失败", tenantId, e);
            return CUSTOMER_MODE;
        }
    }

    /**
     * 更新洞察模式到数据库
     *
     * @param tenantId 租户ID
     * @param mode 洞察模式
     */
    private void updateInsightModeToDatabase(Long tenantId, String mode) {
        String sql = "INSERT INTO qc_ai_agent_ext_config (agent_id, config_key, config_value, description, status, create_time) " +
                    "VALUES (?, ?, ?, ?, '1', CURRENT_TIMESTAMP) " +
                    "ON CONFLICT (agent_id, config_key) DO UPDATE SET " +
                    "config_value = EXCLUDED.config_value, modify_time = CURRENT_TIMESTAMP";

        try {
            // 获取指定租户的数据源配置
            DatasourceConfig config = datasourceService.getDatasourceConfig(tenantId);
            if (config == null) {
                log.warn("租户{}的数据源配置不存在，无法更新洞察模式", tenantId);
                return;
            }

            try (Connection conn = JdbcConnectionBuilder.getConnection(config.toJdbcConnectionProperty());
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setLong(1, CUSTOMER_INSIGHT_AGENT_ID);
                stmt.setString(2, INSIGHT_MODE_CONFIG_KEY);
                stmt.setString(3, mode);
                stmt.setString(4, "客户洞察模式配置：0-客户模式，1-终端经销商模式");

                int result = stmt.executeUpdate();
                log.info("更新租户{}的洞察模式到数据库成功: {}, 影响行数: {}", tenantId, mode, result);
            }

        } catch (Exception e) {
            log.error("更新租户{}的洞察模式到数据库失败", tenantId, e);
            throw new RuntimeException("更新洞察模式到数据库失败", e);
        }
    }
}