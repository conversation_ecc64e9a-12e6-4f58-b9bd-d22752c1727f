package com.qc.agent.app.agent.model.dto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 维度配置数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class InsightDimensionConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维度配置 ID，更新时需要传递
     */
    private Long id;

    /**
     * 关联的智能体 ID
     */
    private Long agentId;

    /**
     * 维度编码
     */
    private String dimensionCode;

    /**
     * 维度展示名称
     */
    private String dimensionName;

    /**
     * 大模型解读提示词
     */
    private String interpretationPrompt;

    /**
     * 结构化提示词片段
     */
    private List<PromptFragmentDTO> interpretationPromptFragments;

    /**
     * 提示词片段值列表
     */
    private List<DimensionPromptValueDTO> promptValues;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 关联的衡量标准列表（支持增删改查）
     */
    private List<InsightStandardDTO> standards;

    /**
     * 维度下挂载数据源数组
     */
    private List<InsightDataSourceDTO> dataSources;

    private String status;
}
