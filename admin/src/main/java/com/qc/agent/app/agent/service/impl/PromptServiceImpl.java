package com.qc.agent.app.agent.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qc.agent.app.agent.mapper.MainPromptMapper;
import com.qc.agent.app.agent.mapper.PromptFragmentMapper;
import com.qc.agent.app.agent.model.dto.MainPromptDTO;
import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.MainPrompt;
import com.qc.agent.app.agent.model.entity.PromptFragment;
import com.qc.agent.app.agent.service.PromptService;

import lombok.extern.slf4j.Slf4j;

/**
 * 提示词服务实现类
 */
@Slf4j
@Service
public class PromptServiceImpl implements PromptService {

    @Resource
    private MainPromptMapper mainPromptMapper;

    @Resource
    private PromptFragmentMapper promptFragmentMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private com.qc.agent.app.agent.mapper.DimensionPromptValueMapper dimensionPromptValueMapper;

    @Override
    public MainPromptDTO getMainPromptByConfigIdAndType(Long dimensionCode, String promptType) {
        MainPrompt mainPrompt = mainPromptMapper.selectByConfigIdAndType(dimensionCode, promptType);
        if (mainPrompt == null) {
            return null;
        }

        MainPromptDTO mainPromptDTO = new MainPromptDTO();
        BeanUtils.copyProperties(mainPrompt, mainPromptDTO);

        // 获取片段
        List<PromptFragment> fragments = promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPrompt.getId());
        if (!CollectionUtils.isEmpty(fragments)) {
            List<PromptFragmentDTO> fragmentDTOs = new ArrayList<>();
            for (PromptFragment fragment : fragments) {
                PromptFragmentDTO fragmentDTO = new PromptFragmentDTO();
                BeanUtils.copyProperties(fragment, fragmentDTO);
                fragmentDTOs.add(fragmentDTO);
            }
            mainPromptDTO.setFragments(fragmentDTOs);
        }

        return mainPromptDTO;
    }

    @Override
    public MainPromptDTO getMainPromptByConfigIdAndType(String dimensionCode, String promptType) {
        MainPrompt mainPrompt = mainPromptMapper.selectByConfigIdAndType(dimensionCode, promptType);
        if (mainPrompt == null) {
            return null;
        }

        MainPromptDTO mainPromptDTO = new MainPromptDTO();
        BeanUtils.copyProperties(mainPrompt, mainPromptDTO);

        // 获取片段
        List<PromptFragment> fragments = promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPrompt.getId());
        if (!CollectionUtils.isEmpty(fragments)) {
            List<PromptFragmentDTO> fragmentDTOs = new ArrayList<>();
            for (PromptFragment fragment : fragments) {
                PromptFragmentDTO fragmentDTO = new PromptFragmentDTO();
                BeanUtils.copyProperties(fragment, fragmentDTO);
                fragmentDTOs.add(fragmentDTO);
            }
            mainPromptDTO.setFragments(fragmentDTOs);
        }

        return mainPromptDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMainPrompt(MainPromptDTO mainPromptDTO) {
        // 先查询是否已存在相同配置ID和类型的主提示词
        MainPrompt existingMainPrompt = mainPromptMapper.selectByConfigIdAndType(mainPromptDTO.getDimensionCode(),
                mainPromptDTO.getPromptType());

        MainPrompt mainPrompt = new MainPrompt();
        BeanUtils.copyProperties(mainPromptDTO, mainPrompt);

        if (existingMainPrompt == null) {
            // 新增
            mainPrompt.setStatus("1");
            mainPromptMapper.insert(mainPrompt);
        } else {
            // 更新
            mainPrompt.setId(existingMainPrompt.getId());
            mainPromptMapper.updateById(mainPrompt);
        }

        // 保存片段
        if (!CollectionUtils.isEmpty(mainPromptDTO.getFragments())) {
            // 先删除现有片段
            promptFragmentMapper.deleteByMainPromptId(mainPrompt.getId());

            // 保存新片段
            List<PromptFragment> fragments = new ArrayList<>();
            for (int i = 0; i < mainPromptDTO.getFragments().size(); i++) {
                PromptFragmentDTO fragmentDto = mainPromptDTO.getFragments().get(i);
                PromptFragment fragment = new PromptFragment();
                BeanUtils.copyProperties(fragmentDto, fragment);
                fragment.setMainPromptId(mainPrompt.getId());
                fragment.setSortOrder(fragmentDto.getSortOrder() != null ? fragmentDto.getSortOrder() : i + 1);
                fragment.setStatus("1");
                fragments.add(fragment);
            }
            promptFragmentMapper.batchInsert(fragments);
        }

        return true;
    }

    @Override
    public List<PromptFragment> getFragmentsByMainPromptId(Long mainPromptId) {
        return promptFragmentMapper.selectByMainPromptIdOrderBySort(mainPromptId);
    }

    @Override
    public JsonNode fragmentsToJsonObject(List<PromptFragment> fragments) {
        if (CollectionUtils.isEmpty(fragments)) {
            return objectMapper.createObjectNode();
        }

        try {
            StringBuilder jsonBuilder = new StringBuilder("{");
            for (int i = 0; i < fragments.size(); i++) {
                PromptFragment fragment = fragments.get(i);
                if (i > 0) {
                    jsonBuilder.append(",");
                }
                jsonBuilder.append("\"").append(fragment.getFragmentKey()).append("\":\"")
                        .append(fragment.getFragmentValue().replace("\"", "\\\"")).append("\"");
            }
            jsonBuilder.append("}");
            return objectMapper.readTree(jsonBuilder.toString());
        } catch (JsonProcessingException e) {
            log.error("转换片段为JSON对象失败", e);
            return objectMapper.createObjectNode();
        }
    }

    @Override
    public List<PromptFragment> jsonObjectToFragments(JsonNode jsonNode, Long mainPromptId) {
        List<PromptFragment> fragments = new ArrayList<>();
        if (jsonNode == null || jsonNode.isEmpty()) {
            return fragments;
        }

        int sortOrder = 1;
        @SuppressWarnings("unchecked")
        java.util.Map<String, Object> map = objectMapper.convertValue(jsonNode, java.util.Map.class);
        for (String key : map.keySet()) {
            PromptFragment fragment = new PromptFragment();
            fragment.setMainPromptId(mainPromptId);
            fragment.setFragmentKey(key);
            fragment.setFragmentValue(jsonNode.get(key).asText());
            fragment.setSortOrder(sortOrder++);
            fragment.setStatus("1");
            fragments.add(fragment);
        }

        return fragments;
    }

    @Override
    public String generatePromptFromFragments(List<PromptFragment> fragments) {
        if (CollectionUtils.isEmpty(fragments)) {
            return "";
        }

        StringBuilder promptBuilder = new StringBuilder();
        for (PromptFragment fragment : fragments) {
            promptBuilder.append(fragment.getFragmentValue());
        }
        return promptBuilder.toString();
    }

    @Override
    public List<com.qc.agent.app.agent.model.entity.DimensionPromptValue> getPromptValuesByDimensionConfigId(
            Long dimensionConfigId) {
        if (dimensionConfigId == null) {
            return new ArrayList<>();
        }
        return dimensionPromptValueMapper.selectByDimensionConfigId(dimensionConfigId);
    }

    @Override
    public boolean savePromptValue(com.qc.agent.app.agent.model.entity.DimensionPromptValue dimensionPromptValue) {
        if (dimensionPromptValue == null) {
            return false;
        }

        // 检查是否已存在相同的维度配置ID和提示词片段ID的记录
        com.qc.agent.app.agent.model.entity.DimensionPromptValue existingPromptValue = dimensionPromptValueMapper
                .selectByDimensionConfigIdAndPromptFragmentId(
                        dimensionPromptValue.getDimensionConfigId(), dimensionPromptValue.getPromptFragmentId());

        if (existingPromptValue == null) {
            // 新增
            dimensionPromptValue.setStatus("1");
            return dimensionPromptValueMapper.insert(dimensionPromptValue) > 0;
        } else {
            // 更新
            dimensionPromptValue.setId(existingPromptValue.getId());
            return dimensionPromptValueMapper.updateById(dimensionPromptValue) > 0;
        }
    }

    @Override
    public boolean savePromptValues(
            List<com.qc.agent.app.agent.model.entity.DimensionPromptValue> dimensionPromptValues) {
        if (CollectionUtils.isEmpty(dimensionPromptValues)) {
            return false;
        }

        // 先删除现有的提示词片段值
        Long dimensionConfigId = dimensionPromptValues.get(0).getDimensionConfigId();
        if (dimensionConfigId != null) {
            dimensionPromptValueMapper.deleteByDimensionConfigId(dimensionConfigId);
        }

        // 批量插入新的提示词片段值
        for (com.qc.agent.app.agent.model.entity.DimensionPromptValue dimensionPromptValue : dimensionPromptValues) {
            dimensionPromptValue.setStatus("1");
        }
        return dimensionPromptValueMapper.batchInsert(dimensionPromptValues) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchImportPrompts(List<DimensionPromptBO> dimensionPrompts) {
        for (DimensionPromptBO bo : dimensionPrompts) {
            MainPrompt mainPrompt = new MainPrompt();
            mainPrompt.setDimensionCode(bo.getDimensionCode());
            mainPrompt.setPromptName(bo.getPromptName());
            mainPrompt.setPromptType("DIMENSION");
            mainPrompt.setStatus("1");
            mainPromptMapper.insert(mainPrompt);

            List<PromptFragment> fragments = bo.getFragments().stream().map(fragment -> {
                PromptFragment pf = new PromptFragment();
                pf.setMainPromptId(mainPrompt.getId());
                pf.setFragmentKey(fragment.getKey());
                pf.setFragmentValue(fragment.getValue());
                pf.setSortOrder(fragment.getSortOrder());
                pf.setStatus("1");
                return pf;
            }).collect(Collectors.toList());
            promptFragmentMapper.batchInsert(fragments);
        }
    }
}