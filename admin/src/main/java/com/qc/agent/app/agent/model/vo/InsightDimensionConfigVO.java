package com.qc.agent.app.agent.model.vo;

import com.qc.agent.app.agent.model.dto.DimensionPromptValueDTO;

import java.io.Serializable;
import java.util.List;

import com.qc.agent.app.agent.model.dto.PromptFragmentDTO;
import com.qc.agent.app.agent.model.entity.InsightStandard;

import lombok.Data;

/**
 * 维度配置视图对象
 *
 * <AUTHOR>
 */
@Data
public class InsightDimensionConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维度配置 ID
     */
    private Long id;

    /**
     * 关联的智能体 ID
     */
    private Long agentId;

    /**
     * 维度编码
     */
    private String dimensionCode;

    /**
     * 维度展示名称
     */
    private String dimensionName;

    /**
     * 大模型解读提示词
     */
    private String interpretationPrompt;

    /**
     * 结构化提示词片段
     */
    private List<PromptFragmentDTO> interpretationPromptFragments;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 关联的衡量标准列表
     */
    private List<InsightStandard> standards;

    /**
     * 该维度下的数据源数组（原有结构，保持向后兼容）
     */
    private List<InsightDataSourceVO> dataSources;

    /**
     * 该维度下的查询业务数组（新结构，用于查询详情接口）
     * 每个查询业务对应一个数据项，如果存在父子关系，在父项中包含子项数组
     */
    private List<InsightQueryBusinessVO> queryBusinesses;

    /**
     * 提示词片段值列表
     */
    private List<DimensionPromptValueDTO> promptValues;

    private String status;
}